import api from './api';

export interface UserData {
  id: number;
  firstName: string;
  lastName: string;
  full_name: string;
  phone: string;
  user_type: 'client' | 'collaborator' | 'admin';
  is_active: boolean;
  date_joined: string;
  last_login: string | null;
  isActive: boolean | null;
  isVerified: boolean;
}

export interface UsersResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: UserData[];
}

export interface UserFilters {
  role?: 'client' | 'collaborator';
  search?: string;
  is_active?: boolean;
  page?: number;
  per_page?: number;
}

export interface UserStats {
  total_users: number;
  active_users: number;
  clients: number;
  collaborators: number;
  new_users_this_month: number;
}

export class UserService {
  /**
   * Get all users with optional filtering
   */
  static async getUsers(filters: UserFilters = {}): Promise<UsersResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters.role) {
        params.append('role', filters.role);
      }
      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.is_active !== undefined) {
        params.append('is_active', filters.is_active.toString());
      }
      if (filters.page) {
        params.append('page', filters.page.toString());
      }
      if (filters.per_page) {
        params.append('per_page', filters.per_page.toString());
      }

      const queryString = params.toString();
      const url = `/auth/admin/users/${queryString ? `?${queryString}` : ''}`;
      
      const response = await api.get<UsersResponse>(url);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent voir la liste des utilisateurs.');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des utilisateurs');
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<UserStats> {
    try {
      const response = await api.get<UserStats>('/auth/admin/users/stats/');
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent voir les statistiques.');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des statistiques');
    }
  }

  /**
   * Get a specific user by ID
   */
  static async getUser(id: number): Promise<UserData> {
    try {
      const response = await api.get<UserData>(`/auth/admin/users/${id}/`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent voir les détails des utilisateurs.');
      }
      if (error.response?.status === 404) {
        throw new Error('Utilisateur non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'utilisateur');
    }
  }

  /**
   * Toggle user active status (Admin only)
   */
  static async toggleUserStatus(id: number): Promise<UserData> {
    try {
      const response = await api.patch<UserData>(`/auth/admin/users/${id}/toggle-status/`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent modifier le statut des utilisateurs.');
      }
      if (error.response?.status === 404) {
        throw new Error('Utilisateur non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la modification du statut');
    }
  }

  /**
   * Delete a user (Admin only)
   */
  static async deleteUser(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.delete<{ success: boolean; message: string }>(`/auth/admin/users/${id}/`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent supprimer des utilisateurs.');
      }
      if (error.response?.status === 404) {
        throw new Error('Utilisateur non trouvé');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'utilisateur');
    }
  }

  /**
   * Export users data (Admin only)
   */
  static async exportUsers(filters: UserFilters = {}): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      
      if (filters.role) {
        params.append('role', filters.role);
      }
      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.is_active !== undefined) {
        params.append('is_active', filters.is_active.toString());
      }

      const queryString = params.toString();
      const url = `/auth/admin/users/export/${queryString ? `?${queryString}` : ''}`;
      
      const response = await api.get(url, {
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error('Accès refusé. Seuls les administrateurs peuvent exporter les données.');
      }
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'export des données');
    }
  }
}

export default UserService;
