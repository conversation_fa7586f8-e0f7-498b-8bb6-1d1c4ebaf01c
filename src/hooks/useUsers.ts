import { useState, useEffect, useCallback } from 'react';
import { UserService, UserData, UsersResponse, UserFilters, UserStats } from '@/services/users';

interface UseUsersReturn {
  users: UserData[];
  loading: boolean;
  error: string | null;
  pagination: {
    count: number;
    next: string | null;
    previous: string | null;
    currentPage: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
  stats: UserStats | null;
  statsLoading: boolean;
  fetchUsers: (filters?: UserFilters) => Promise<void>;
  fetchStats: () => Promise<void>;
  toggleUserStatus: (id: number) => Promise<void>;
  deleteUser: (id: number) => Promise<void>;
  exportUsers: (filters?: UserFilters) => Promise<void>;
  clearError: () => void;
}

export const useUsers = (initialFilters: UserFilters = {}): UseUsersReturn => {
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    count: 0,
    next: null as string | null,
    previous: null as string | null,
    currentPage: 1,
    totalPages: 1,
    hasNext: false,
    hasPrevious: false,
  });
  const [stats, setStats] = useState<UserStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  const calculatePagination = useCallback((response: UsersResponse, currentPage: number, perPage: number) => {
    const totalPages = Math.ceil(response.count / perPage);
    return {
      count: response.count,
      next: response.next,
      previous: response.previous,
      currentPage,
      totalPages,
      hasNext: !!response.next,
      hasPrevious: !!response.previous,
    };
  }, []);

  const fetchUsers = useCallback(async (filters: UserFilters = {}) => {
    try {
      setLoading(true);
      setError(null);
      
      const mergedFilters = { ...initialFilters, ...filters };
      const response = await UserService.getUsers(mergedFilters);
      
      setUsers(response.results);
      setPagination(calculatePagination(
        response, 
        mergedFilters.page || 1, 
        mergedFilters.per_page || 10
      ));
    } catch (err: any) {
      setError(err.message);
      setUsers([]);
      setPagination({
        count: 0,
        next: null,
        previous: null,
        currentPage: 1,
        totalPages: 1,
        hasNext: false,
        hasPrevious: false,
      });
    } finally {
      setLoading(false);
    }
  }, [initialFilters, calculatePagination]);

  const fetchStats = useCallback(async () => {
    try {
      setStatsLoading(true);
      const statsData = await UserService.getUserStats();
      setStats(statsData);
    } catch (err: any) {
      console.error('Error fetching user stats:', err.message);
      // Don't set error for stats as it's not critical
    } finally {
      setStatsLoading(false);
    }
  }, []);

  const toggleUserStatus = useCallback(async (id: number) => {
    try {
      setError(null);
      const updatedUser = await UserService.toggleUserStatus(id);
      
      // Update the user in the current list
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === id ? updatedUser : user
        )
      );
    } catch (err: any) {
      setError(err.message);
      throw err; // Re-throw to allow component to handle it
    }
  }, []);

  const deleteUser = useCallback(async (id: number) => {
    try {
      setError(null);
      await UserService.deleteUser(id);
      
      // Remove the user from the current list
      setUsers(prevUsers => prevUsers.filter(user => user.id !== id));
      
      // Update pagination count
      setPagination(prev => ({
        ...prev,
        count: prev.count - 1,
      }));
    } catch (err: any) {
      setError(err.message);
      throw err; // Re-throw to allow component to handle it
    }
  }, []);

  const exportUsers = useCallback(async (filters: UserFilters = {}) => {
    try {
      setError(null);
      const blob = await UserService.exportUsers(filters);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message);
      throw err; // Re-throw to allow component to handle it
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchUsers(initialFilters);
  }, [fetchUsers]);

  return {
    users,
    loading,
    error,
    pagination,
    stats,
    statsLoading,
    fetchUsers,
    fetchStats,
    toggleUserStatus,
    deleteUser,
    exportUsers,
    clearError,
  };
};

export default useUsers;
